# This file was autogenerated by uv via the following command:
#    uv pip compile requirements-in.txt --python-version 3.12 --python-platform windows -o win.txt
aiofiles==24.1.0
    # via -r requirements-in.txt
anyio==4.9.0
    # via httpx
attrs==25.3.0
    # via
    #   service-identity
    #   twisted
automat==24.8.1
    # via twisted
brotli==1.1.0
    # via -r requirements-in.txt
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via requests
colorama==0.4.6
    # via
    #   -r requirements-in.txt
    #   loguru
    #   tqdm
constantly==23.10.4
    # via twisted
cryptography==44.0.2
    # via
    #   pyopenssl
    #   scrapy
    #   service-identity
cssselect==1.3.0
    # via
    #   parsel
    #   scrapy
darkdetect==0.8.0
    # via pyqt-fluent-widgets
decorator==5.2.1
    # via jsonpath-rw
defusedxml==0.7.1
    # via scrapy
filelock==3.18.0
    # via tldextract
h11==0.16.0
    # via
    #   -r requirements-in.txt
    #   httpcore
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via -r requirements-in.txt
hyperframe==6.1.0
    # via h2
hyperlink==21.0.0
    # via twisted
idna==3.10
    # via
    #   anyio
    #   httpx
    #   hyperlink
    #   requests
    #   tldextract
incremental==24.7.2
    # via twisted
itemadapter==0.11.0
    # via
    #   itemloaders
    #   scrapy
itemloaders==1.3.2
    # via scrapy
jmespath==1.0.1
    # via
    #   itemloaders
    #   parsel
jsonpath-rw==1.4.0
    # via -r requirements-in.txt
loguru==0.7.3
    # via -r requirements-in.txt
lxml==5.3.1
    # via
    #   -r requirements-in.txt
    #   parsel
    #   scrapy
markdown==3.7
    # via -r requirements-in.txt
packaging==24.2
    # via
    #   parsel
    #   scrapy
parsel==1.10.0
    # via
    #   itemloaders
    #   scrapy
pillow==11.3.0
    # via -r requirements-in.txt
pillow-avif-plugin==1.5.2
    # via -r requirements-in.txt
ply==3.11
    # via jsonpath-rw
polib==1.2.0
    # via -r requirements-in.txt
protego==0.4.0
    # via scrapy
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   service-identity
pyasn1-modules==0.4.2
    # via service-identity
pycparser==2.22
    # via cffi
pydispatcher==2.0.7
    # via scrapy
pyexecjs==1.5.1
    # via -r requirements-in.txt
pyopenssl==25.0.0
    # via scrapy
pyperclip==1.9.0
    # via uncurl
pyqt-fluent-widgets==1.7.6
    # via -r requirements-in.txt
pyqt5==5.15.11
    # via
    #   -r requirements-in.txt
    #   pyqt-fluent-widgets
    #   pyqtwebengine
pyqt5-frameless-window==0.5.1
    # via pyqt-fluent-widgets
pyqt5-qt5==5.15.2
    # via pyqt5
pyqt5-sip==12.17.0
    # via
    #   pyqt5
    #   pyqtwebengine
pyqtwebengine==5.15.7
    # via -r requirements-in.txt
pyqtwebengine-qt5==5.15.2
    # via pyqtwebengine
pywin32==310
    # via pyqt5-frameless-window
pyyaml==6.0.2
    # via -r requirements-in.txt
queuelib==1.7.0
    # via scrapy
requests==2.32.4
    # via
    #   -r requirements-in.txt
    #   requests-file
    #   tldextract
requests-file==2.1.0
    # via tldextract
scrapy==2.13.2
    # via -r requirements-in.txt
service-identity==24.2.0
    # via scrapy
setuptools==80.7.1
    # via
    #   -r requirements-in.txt
    #   incremental
    #   zope-interface
six==1.17.0
    # via
    #   jsonpath-rw
    #   pyexecjs
    #   uncurl
sniffio==1.3.1
    # via anyio
tldextract==5.1.3
    # via scrapy
tqdm==4.67.1
    # via -r requirements-in.txt
twisted==24.11.0
    # via scrapy
typing-extensions==4.13.0
    # via
    #   anyio
    #   pyopenssl
    #   twisted
uncurl==0.0.11
    # via -r requirements-in.txt
urllib3==2.5.0
    # via
    #   -r requirements-in.txt
    #   requests
uv==0.7.2
    # via -r requirements-in.txt
w3lib==2.3.1
    # via
    #   parsel
    #   scrapy
win32-setctime==1.2.0
    # via loguru
zope-interface==7.2
    # via
    #   scrapy
    #   twisted
